use anyhow::Result;
use async_trait::async_trait;
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt;

pub mod executor;
pub mod file_ops;
pub mod registry;
pub mod schema;
pub mod search;
pub mod shell_ops;

pub use executor::ToolExecutor;
pub use registry::ToolRegistry;
pub use schema::ToolSchema;

/// Result of a tool execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    /// Whether the tool execution was successful
    pub success: bool,
    /// Output from the tool execution
    pub output: String,
    /// Error message if execution failed
    pub error: Option<String>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

impl ToolResult {
    pub fn success(output: String) -> Self {
        Self {
            success: true,
            output,
            error: None,
            metadata: HashMap::new(),
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            output: String::new(),
            error: Some(error),
            metadata: HashMap::new(),
        }
    }

    pub fn with_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

impl fmt::Display for ToolResult {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if self.success {
            write!(f, "✅ {}", self.output)
        } else {
            write!(f, "❌ {}", self.error.as_ref().unwrap_or(&self.output))
        }
    }
}

/// Tool execution context
#[derive(Debug, Clone)]
pub struct ToolContext {
    /// Current working directory
    pub working_dir: std::path::PathBuf,
    /// Environment variables
    pub env_vars: HashMap<String, String>,
    /// User confirmation callback
    pub confirm_callback: Option<fn(&str) -> bool>,
    /// Safety settings
    pub safety_enabled: bool,
    /// Maximum execution time in seconds
    pub timeout_seconds: u64,
}

impl Default for ToolContext {
    fn default() -> Self {
        Self {
            working_dir: std::env::current_dir().unwrap_or_default(),
            env_vars: std::env::vars().collect(),
            confirm_callback: None,
            safety_enabled: true,
            timeout_seconds: 300, // 5 minutes
        }
    }
}

/// Trait that all tools must implement
#[async_trait]
pub trait Tool: Send + Sync + std::fmt::Debug {
    /// Get the tool's name
    fn name(&self) -> &str;
    
    /// Get the tool's description
    fn description(&self) -> &str;
    
    /// Get the JSON schema for the tool's parameters
    fn schema(&self) -> ToolSchema;
    
    /// Execute the tool with the given parameters
    async fn execute(
        &self,
        params: serde_json::Value,
        context: &ToolContext,
    ) -> Result<ToolResult>;
    
    /// Check if the tool requires user confirmation
    fn requires_confirmation(&self) -> bool {
        false
    }
    
    /// Check if the tool is safe to run without confirmation
    fn is_safe(&self) -> bool {
        true
    }
    
    /// Get tool category for organization
    fn category(&self) -> ToolCategory {
        ToolCategory::General
    }
}

/// Tool categories for organization
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ToolCategory {
    /// File system operations
    FileSystem,
    /// Shell and command execution
    Shell,
    /// Search and text processing
    Search,
    /// Network operations
    Network,
    /// General utilities
    General,
}

impl fmt::Display for ToolCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ToolCategory::FileSystem => write!(f, "File System"),
            ToolCategory::Shell => write!(f, "Shell"),
            ToolCategory::Search => write!(f, "Search"),
            ToolCategory::Network => write!(f, "Network"),
            ToolCategory::General => write!(f, "General"),
        }
    }
}

/// Tool call request from LLM
#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct ToolCall {
    /// Name of the tool to call
    pub name: String,
    /// Parameters to pass to the tool
    pub parameters: serde_json::Value,
    /// Unique identifier for this tool call
    pub id: Option<String>,
}

/// Tool call response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResponse {
    /// The original tool call
    pub call: ToolCall,
    /// Result of the execution
    pub result: ToolResult,
    /// Execution time in milliseconds
    pub execution_time_ms: u64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tool_result_display() {
        let success = ToolResult::success("Operation completed".to_string());
        assert_eq!(format!("{}", success), "✅ Operation completed");

        let error = ToolResult::error("Something went wrong".to_string());
        assert_eq!(format!("{}", error), "❌ Something went wrong");
    }

    #[test]
    fn test_tool_result_with_metadata() {
        let result = ToolResult::success("Done".to_string())
            .with_metadata("duration".to_string(), serde_json::json!(1.5));
        
        assert!(result.metadata.contains_key("duration"));
        assert_eq!(result.metadata["duration"], serde_json::json!(1.5));
    }

    #[test]
    fn test_tool_category_display() {
        assert_eq!(format!("{}", ToolCategory::FileSystem), "File System");
        assert_eq!(format!("{}", ToolCategory::Shell), "Shell");
        assert_eq!(format!("{}", ToolCategory::Search), "Search");
    }
}
